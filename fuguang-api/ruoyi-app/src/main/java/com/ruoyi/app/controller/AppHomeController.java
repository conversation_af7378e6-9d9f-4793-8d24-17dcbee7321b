package com.ruoyi.app.controller;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.fuguang.domain.*;
import com.ruoyi.fuguang.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;

/**
 * APP首页接口
 *
 * <AUTHOR>
 */
@Api(tags = "APP首页接口")
@RestController("appHomeApiController")
@RequestMapping("/app/home")
public class AppHomeController extends BaseController
{
    @Autowired
    private IAppTaskService appTaskService;

    @Autowired
    private IAppNoticeService appNoticeService;

    @Autowired
    private IAppFunctionService appFunctionService;

    @Autowired
    private IAppConfigService appConfigService;

    @Autowired
    private IMallProductService mallProductService;

    @Autowired
    private IMerchantApplicationService merchantApplicationService;

    /**
     * 获取首页数据
     * 获取APP首页展示的各种数据，包括标语、通知、热门任务等
     *
     * @param longitude 用户当前经度，用于推荐附近任务
     * @param latitude 用户当前纬度，用于推荐附近任务
     * @return 首页数据
     */
    @Anonymous
    @ApiOperation(value = "获取首页数据",
                  notes = "获取APP首页展示数据，包括首页标语、系统通知、热门任务等，支持基于地理位置的任务推荐")
    @ApiResponses({
        @ApiResponse(code = 200, message = "获取成功，返回首页数据")
    })
    @GetMapping("/index")
    public AjaxResult getHomeData(
            @ApiParam(value = "用户当前经度", required = false) @RequestParam(required = false) String longitude,
            @ApiParam(value = "用户当前纬度", required = false) @RequestParam(required = false) String latitude)
    {
        Map<String, Object> data = new HashMap<>();

        // 获取首页标语
        String slogan = appConfigService.getHomeSlogan();
        data.put("slogan", slogan);

        // 获取最新系统通知（滚动播放）
        List<AppNotice> notices = appNoticeService.selectLatestSystemNotices(3);
        data.put("notices", notices);

        // 获取热门任务列表（最近十条）
        List<AppTask> hotTasks = appTaskService.selectHotTaskList(longitude, latitude, 10);
        data.put("hotTasks", hotTasks);

        return success(data);
    }

    /**
     * 全局搜索功能
     * 支持搜索任务、商品、商家等内容
     *
     * @param keyword 搜索关键词
     * @param type 搜索类型（task-任务，product-商品，merchant-商家）
     * @return 搜索结果
     */
    @Anonymous
    @ApiOperation(value = "搜索任务、商品、商家",
                  notes = "全局搜索功能，支持搜索任务、商品、商家等内容")
    @ApiResponses({
        @ApiResponse(code = 200, message = "搜索成功，返回搜索结果")
    })
    @GetMapping("/search")
    public TableDataInfo search(
            @ApiParam(value = "搜索关键词", required = true) @RequestParam String keyword,
            @ApiParam(value = "搜索类型：task-任务，product-商品，merchant-商家", required = false) @RequestParam(defaultValue = "task") String type)
    {
        if ("task".equals(type)) {
            // 搜索任务
            startPage();
            AppTask searchTask = new AppTask();
            searchTask.setTaskTitle(keyword);
            List<AppTask> tasks = appTaskService.selectAppTaskList(searchTask);
            return getDataTable(tasks);
        }
        if ("product".equals(type)) {
            // 搜索任务
            startPage();
            List<MallProduct> tasks = mallProductService.searchProducts(keyword);
            return getDataTable(tasks);
        }
        if ("merchant".equals(type)) {
            // 搜索任务
            startPage();
            MerchantApplication searchMerchant = new MerchantApplication();
            searchMerchant.setShopName(keyword);
            List<MerchantApplication> tasks = merchantApplicationService.selectMerchantApplicationList(searchMerchant);
            return getDataTable(tasks);
        }
        return getDataTable(new ArrayList<>());
    }

    /**
     * 获取通知列表
     */
    @Anonymous
    @ApiOperation("获取通知列表")
    @GetMapping("/notices")
    public AjaxResult getNotices(AppNotice queryNotice)
    {
        Long userId = getUserId();
        // 设置分页参数
        startPage();
        List<AppNotice> notices = appNoticeService.selectNoticesByUserWithFilter(userId, queryNotice);
        return success(notices);
    }

    /**
     * 标记通知为已读
     */
    @Anonymous
    @ApiOperation("标记通知为已读")
    @GetMapping("/notice/read")
    public AjaxResult markNoticeAsRead(@RequestParam Long noticeId)
    {
        Long userId = getUserId();
        int result = appNoticeService.markNoticeAsRead(noticeId, userId);
        return toAjax(result);
    }

    /**
     * 获取未读通知数量
     */
    @Anonymous
    @ApiOperation("获取未读通知数量")
    @GetMapping("/notice/unread-count")
    public AjaxResult getUnreadNoticeCount()
    {
        Long userId = getUserId();
        int count = appNoticeService.countUnreadNotices(userId);
        return success(count);
    }

    /**
     * 批量标记通知为已读
     */
    @Anonymous
    @ApiOperation("批量标记通知为已读")
    @PostMapping("/notice/mark-all-read")
    public AjaxResult markAllNoticesAsRead()
    {
        Long userId = getUserId();
        int result = appNoticeService.markAllNoticesAsRead(userId);
        return toAjax(result);
    }

    /**
     * 清空所有通知
     */
    @Anonymous
    @ApiOperation("清空所有通知")
    @PostMapping("/notice/clear-all")
    public AjaxResult clearAllNotices()
    {
        Long userId = getUserId();
        int result = appNoticeService.clearAllNotices(userId);
        return toAjax(result);
    }


    /**
     * 二维码扫描处理
     */
    @ApiOperation("二维码扫描处理")
    @PostMapping("/qrcode/scan")
    public AjaxResult scanQrCode(@RequestBody Map<String, String> params)
    {
        String qrContent = params.get("content");
        
        // TODO: 根据二维码内容进行相应处理
        // 可能是任务二维码、商品二维码等
        
        Map<String, Object> result = new HashMap<>();
        result.put("type", "unknown");
        result.put("content", qrContent);
        
        return success(result);
    }
    /**
     * 获取购物专区配置
     */
    @Anonymous
    @ApiOperation("获取购物专区配置")
    @GetMapping("/shopping")
    public AjaxResult getShoppingConfig()
    {
        try {
            // 获取图片配置
            AppConfig appConfig = appConfigService.selectAppConfigByKey("app.shopping.title");
            return success(appConfig);
        } catch (Exception e) {
            return error("获取购物专区配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取首页功能配置
     */
    @Anonymous
    @ApiOperation("获取首页功能配置")
    @GetMapping("/functions")
    public AjaxResult getHomeFunctions()
    {
        try {
            // 获取首页显示的功能列表（displayLocation = '0'）
            List<AppFunction> functions = appFunctionService.selectEnabledAppFunctionListByLocation("0");
            return success(functions);
        } catch (Exception e) {
            return error("获取首页功能配置失败：" + e.getMessage());
        }
    }
}
